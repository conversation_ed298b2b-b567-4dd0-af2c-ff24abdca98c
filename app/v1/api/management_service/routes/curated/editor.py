from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any
from bson import ObjectId
from pymongo.errors import PyMongoError

from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName
from datetime import datetime, timezone
from app.shared.utils.mongo_helper import serialize_mongo_doc

loggers = setup_new_logging(__name__)


router = APIRouter( 
    tags=["Curated Editor"],
    responses={404: {"description": "Not found"}}
)




@router.post("/generate", response_model=Dict[str, Any])
async def generate_curated_content(
    content: str,
    background_tasks: BackgroundTasks,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Generate curated content based on the provided input.

    Args:
        content: The content to be generated.

    """
    try:
        background_tasks.add_task(
            save_to_database,
            content,
            current_user
        )
        # Simulate content generation logic
        generated_content = {
            "content": content,
            "generated_by": current_user.user.id,
            "status": "success",
            "message": "Content generated successfully"
        }
        return generated_content
    except Exception as e:
        loggers.error(f"Error generating curated content: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate curated content")


@router.post("/get_prompts", response_model=Dict[str, Any])
async def get_prompts(
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Retrieve prompts for the current user.
    
    Args:
        current_user (UserTenantDB): The current user information.
    
    Returns:
        Dict[str, Any]: A dictionary containing the prompts.
    """
    try:
        # Simulate fetching prompts from the database
        prompts = await current_user.async_db[CollectionName.editor_prompts].find(
            {"user_id": current_user.user.id}
        ).to_list(length=None)
        
        return {"data":serialize_mongo_doc(prompts)}
    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")

async def save_to_database(
    content: str,
    current_user: UserTenantDB
):
    """
    Save generated content to the database.

    Args:
        content (str): The content to be saved.
        current_user (UserTenantDB): The current user information.

    """
    try:
        # Simulate saving to database logic
        loggers.info(f"Saving content {content} for user {current_user.user.id}")

        await current_user.async_db[CollectionName.editor_prompts].insert_one({
            "content": content,
            "user_id": current_user.user.id,
            "task_set_id": ObjectId(),  # Assuming a new task set ID is generated
            "created_at": datetime.now(timezone.utc),
            "status": "pending"
        })
        # Here you would typically use a database client to insert the content
    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")