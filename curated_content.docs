# Curated Content API Routes

Base URL: `/v1/management/curated`

## GET /themes

**Request:**
```
GET /v1/management/curated/themes?page=1&limit=50&search=culture&category=culture&is_active=true
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes?page=1&limit=50&search=culture&category=culture&is_active=true' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "name": "नेपाली संस्कृति",
      "name_en": "Nepali Culture",
      "description": "नेपाली संस्कृति र परम्पराका बारेमा",
      "description_en": "About Nepali culture and traditions",
      "category": "culture",
      "icon": "🏛️",
      "color": "#FF6B6B",
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 50,
    "total": 25,
    "total_pages": 1
  }
}
```

## GET /themes/{theme_id}

**Request:**
```
GET /v1/management/curated/themes/507f1f77bcf86cd799439011?page=1&limit=20&difficulty_level=2&status=completed&gentype=primary
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes/685e2955f928ae494af5e978?page=1&limit=20' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZ2VudCIsInRlbmFudF9pZCI6IjY4MzgzYzU2YjUzZGFmZTliOWU4NWZjNiIsImV4cCI6MTc1MTAxMzE0M30.YfJBsNJDFE5p15meBnDd0DD8SqshPKmT6DzRv6U1zgA'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "theme_id": "507f1f77bcf86cd799439011",
      "title": "नेपाली त्योहारहरू",
      "title_en": "Nepali Festivals",
      "description": "नेपालका मुख्य त्योहारहरूका बारेमा प्रश्नहरू",
      "description_en": "Questions about major festivals of Nepal",
      "difficulty_level": 2,
      "status": "completed",
      "gentype": "primary",
      "task_item_ids": ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014"],
      "total_items": 10,
      "created_at": "2024-01-01T10:00:00Z",
      "theme": {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "culture"
      }
  
  }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 15,
    "total_pages": 1
  },
}
```




## GET /filtered

**Request:**
```
GET /v1/management/curated/filtered?page=1&limit=20&theme_id=507f1f77bcf86cd799439011&difficulty_level=2&status=completed&gentype=primary
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filtered?page=1&limit=20&theme_id=507f1f77bcf86cd799439011&difficulty_level=2&status=completed&gentype=primary' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "theme_id": "507f1f77bcf86cd799439011",
      "title": "नेपाली त्योहारहरू",
      "title_en": "Nepali Festivals",
      "description": "नेपालका मुख्य त्योहारहरूका बारेमा प्रश्नहरू",
      "description_en": "Questions about major festivals of Nepal",
      "difficulty_level": 2,
      "status": "completed",
      "gentype": "primary",
      "task_item_ids": ["507f1f77bcf86cd799439013"],
      "total_items": 10,
      "created_at": "2024-01-01T10:00:00Z",
      "theme": {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "culture"
      }
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "total_pages": 3
  }
}
```

## GET /theme/{theme_id}

**Request:**
```
GET /v1/management/curated/theme/507f1f77bcf86cd799439011
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "name": "नेपाली संस्कृति",
    "name_en": "Nepali Culture",
    "description": "नेपाली संस्कृति र परम्पराका बारेमा",
    "description_en": "About Nepali culture and traditions",
    "category": "culture",
    "icon": "🏛️",
    "color": "#FF6B6B",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z",
    "statistics": {
      "total_content_sets": 15,
      "total_content_items": 150,
      "average_items_per_set": 10.0
    }
  },
  "message": "Theme details retrieved successfully",
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```




## POST /generate

**Request:**
```
POST /v1/management/curated/generate
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "content": "Generate questions about Nepali festivals"
}
```

**Response:**
```json
{
  "content": "Generate questions about Nepali festivals",
  "generated_by": "user_id_here",
  "status": "success",
  "message": "Content generated successfully"
}
```

## POST /get_prompts

**Request:**
```
POST /v1/management/curated/get_prompts
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439015",
      "content": "Generate questions about Nepali festivals",
      "user_id": "user_id_here",
      "task_set_id": "507f1f77bcf86cd799439016",
      "created_at": "2024-01-01T10:00:00Z",
      "status": "pending"
    }
  ]
}
```

## GET /filter-options

**Request:**
```
GET /v1/management/curated/filter-options
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filter-options' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "themes": [
      {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture"
      },
      {
        "id": "507f1f77bcf86cd799439012",
        "name": "नेपाली भूगोल",
        "name_en": "Nepali Geography"
      }
    ],
    "status_values": ["pending", "completed", "in_progress"],
    "gentype_values": ["primary", "follow_up", "supplementary"],
    "difficulty_levels": [1, 2, 3]
  },
  "message": "Filter options retrieved successfully",
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

## Filter Values

**Categories:** culture, geography, history, language, literature, science, sports, politics, economy, religion, art, music, dance, food, festivals, traditions, customs, wildlife, nature, tourism, education, technology, health, agriculture, business, entertainment

**Status:** pending, completed, in_progress, cancelled

**Gentype:** primary, follow_up, supplementary, review

**Difficulty:** 1 (easy), 2 (medium), 3 (hard)
